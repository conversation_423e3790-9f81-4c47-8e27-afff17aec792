"""
FastLLaMA Model Implementation.

Main model class that integrates all components: hierarchical attention,
context compression, dynamic layer scaling, and memory optimizations.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, List, Union, Dict, Any
import warnings

from ..config import FastLLaMAConfig
from .layers import (
    FastLLaMADecoderLayer,
    FastLLaMAEmbedding,
    FastLLaMARotaryEmbedding,
    FastLLaMARMSNorm
)
from .compression import ContextCompressor


class FastLLaMAModel(nn.Module):
    """
    FastLLaMA model with hierarchical attention and context compression.

    This model implements the complete FastLLaMA architecture with:
    - Hierarchical attention mechanism
    - Context compression for long sequences
    - Dynamic layer scaling with early exits
    - Memory optimizations
    """

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.vocab_size = config.vocab_size
        self.hidden_size = config.hidden_size
        self.num_hidden_layers = config.num_hidden_layers

        # Embeddings
        self.embed_tokens = FastLLaMAEmbedding(config)

        # Rotary position embeddings
        self.rotary_emb = FastLLaMARotaryEmbedding(
            config.head_dim,
            max_position_embeddings=config.max_position_embeddings,
            base=config.rope_theta
        )

        # Decoder layers
        self.layers = nn.ModuleList([
            FastLLaMADecoderLayer(config, layer_idx)
            for layer_idx in range(config.num_hidden_layers)
        ])

        # Final layer norm
        self.norm = FastLLaMARMSNorm(config.hidden_size, eps=config.rms_norm_eps)

        # Context compression module
        if config.enable_context_compression:
            self.context_compressor = ContextCompressor(config)
        else:
            self.context_compressor = None

        # Language modeling head
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)

        # Tie embeddings and output weights (optional)
        if hasattr(config, 'tie_word_embeddings') and config.tie_word_embeddings:
            self.lm_head.weight = self.embed_tokens.embed_tokens.weight

        # Gradient checkpointing
        self.gradient_checkpointing = config.use_gradient_checkpointing

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=self.config.initializer_range)

    def get_input_embeddings(self):
        return self.embed_tokens

    def set_input_embeddings(self, value):
        self.embed_tokens = value

    def get_output_embeddings(self):
        return self.lm_head

    def set_output_embeddings(self, new_embeddings):
        self.lm_head = new_embeddings

    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        enable_early_exit: Optional[bool] = None,
    ) -> Union[Tuple, Dict[str, Any]]:

        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        use_cache = use_cache if use_cache is not None else self.config.use_cache
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        enable_early_exit = enable_early_exit if enable_early_exit is not None else self.config.enable_early_exit

        # Retrieve input_ids and inputs_embeds
        if input_ids is not None and inputs_embeds is not None:
            raise ValueError("You cannot specify both input_ids and inputs_embeds at the same time")
        elif input_ids is not None:
            batch_size, seq_length = input_ids.shape
        elif inputs_embeds is not None:
            batch_size, seq_length, _ = inputs_embeds.shape
        else:
            raise ValueError("You have to specify either input_ids or inputs_embeds")

        # Handle past key values
        if past_key_values is None:
            past_length = 0
            past_key_values = tuple([None] * len(self.layers))
        else:
            past_length = past_key_values[0][0].shape[2]

        # Position IDs
        if position_ids is None:
            device = input_ids.device if input_ids is not None else inputs_embeds.device
            position_ids = torch.arange(
                past_length, seq_length + past_length, dtype=torch.long, device=device
            )
            position_ids = position_ids.unsqueeze(0).view(-1, seq_length)

        # Embeddings
        if inputs_embeds is None:
            inputs_embeds = self.embed_tokens(input_ids)

        # Context compression for long sequences
        if self.context_compressor is not None and seq_length > self.config.compression_ratio * 2:
            compressed_embeds, compression_quality = self.context_compressor(inputs_embeds, attention_mask)
            # Use compressed embeddings for processing
            hidden_states = compressed_embeds
            # Adjust attention mask for compressed sequence
            if attention_mask is not None:
                compressed_length = compressed_embeds.size(1)
                attention_mask = attention_mask[:, :compressed_length]
        else:
            hidden_states = inputs_embeds
            compression_quality = None

        # Attention mask
        if attention_mask is None:
            attention_mask = torch.ones(
                (batch_size, hidden_states.size(1)), dtype=torch.bool, device=hidden_states.device
            )

        # Create causal mask
        causal_mask = self._prepare_decoder_attention_mask(
            attention_mask, hidden_states.shape[:2], hidden_states, past_length
        )

        # RoPE
        # cos, sin = self.rotary_emb(hidden_states, seq_len=hidden_states.size(1))

        # Decoder layers
        all_hidden_states = () if output_hidden_states else None
        all_self_attns = () if output_attentions else None
        next_decoder_cache = () if use_cache else None
        early_exit_outputs = []

        for idx, decoder_layer in enumerate(self.layers):
            if output_hidden_states:
                all_hidden_states += (hidden_states,)

            past_key_value = past_key_values[idx] if past_key_values is not None else None

            # Gradient checkpointing
            if self.gradient_checkpointing and self.training:
                def create_custom_forward(module):
                    def custom_forward(*inputs):
                        return module(*inputs, output_attentions, use_cache)
                    return custom_forward

                layer_outputs = torch.utils.checkpoint.checkpoint(
                    create_custom_forward(decoder_layer),
                    hidden_states,
                    causal_mask,
                    position_ids,
                    past_key_value,
                )
            else:
                layer_outputs = decoder_layer(
                    hidden_states,
                    attention_mask=causal_mask,
                    position_ids=position_ids,
                    past_key_value=past_key_value,
                    output_attentions=output_attentions,
                    use_cache=use_cache,
                )

            hidden_states = layer_outputs[0]

            if use_cache:
                next_decoder_cache += (layer_outputs[-1],)

            if output_attentions:
                all_self_attns += (layer_outputs[1],)

            # Check for early exit
            if enable_early_exit and len(layer_outputs) > 3:
                early_exit_output = layer_outputs[3]
                early_exit_outputs.append(early_exit_output)

                # Decide whether to exit early
                if not self.training and early_exit_output["confidence"].mean() > self.config.confidence_threshold:
                    # Early exit - return current predictions
                    logits = early_exit_output["logits"]
                    break

        # Final layer norm
        hidden_states = self.norm(hidden_states)

        # Add hidden states from the last decoder layer
        if output_hidden_states:
            all_hidden_states += (hidden_states,)

        # Language modeling head
        if hasattr(self, 'lm_head'):
            logits = self.lm_head(hidden_states)
        else:
            logits = hidden_states

        # Calculate loss if labels are provided
        loss = None
        if labels is not None:
            # Handle case where sequence length might have changed due to compression
            if logits.size(1) != labels.size(1):
                # If compression was applied, we need to adjust labels
                if logits.size(1) < labels.size(1):
                    # Truncate labels to match logits
                    labels = labels[:, :logits.size(1)]
                else:
                    # This shouldn't happen, but handle it gracefully
                    logits = logits[:, :labels.size(1), :]

            # Shift so that tokens < n predict n
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()

            # Ensure shapes match
            if shift_logits.size(1) != shift_labels.size(1):
                min_len = min(shift_logits.size(1), shift_labels.size(1))
                shift_logits = shift_logits[:, :min_len, :]
                shift_labels = shift_labels[:, :min_len]

            # Flatten the tokens
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)  # Ignore padding tokens
            shift_logits = shift_logits.view(-1, self.config.vocab_size)
            shift_labels = shift_labels.view(-1)
            # Enable model parallelism
            shift_labels = shift_labels.to(shift_logits.device)
            loss = loss_fct(shift_logits, shift_labels)

        if not return_dict:
            output = (logits,) + layer_outputs[1:]
            return (loss,) + output if loss is not None else output

        return {
            "loss": loss,
            "logits": logits,
            "past_key_values": next_decoder_cache,
            "hidden_states": all_hidden_states,
            "attentions": all_self_attns,
            "early_exit_outputs": early_exit_outputs if early_exit_outputs else None,
            "compression_quality": compression_quality,
        }

    def _prepare_decoder_attention_mask(self, attention_mask, input_shape, inputs_embeds, past_key_values_length):
        """Prepare causal attention mask."""
        # Create causal mask
        batch_size, seq_length = input_shape
        mask = torch.full((seq_length, seq_length), float("-inf"), device=inputs_embeds.device)
        mask_cond = torch.arange(mask.size(-1), device=inputs_embeds.device)
        mask.masked_fill_(mask_cond < (mask_cond + 1).view(mask.size(-1), 1), 0)
        mask = mask.to(inputs_embeds.dtype)

        if past_key_values_length > 0:
            mask = torch.cat([torch.zeros(seq_length, past_key_values_length, dtype=inputs_embeds.dtype, device=inputs_embeds.device), mask], dim=-1)

        # Expand mask
        expanded_mask = mask[None, None, :, :].expand(batch_size, 1, seq_length, seq_length + past_key_values_length)

        # Combine with attention mask
        if attention_mask is not None:
            # Convert boolean mask to float and invert
            if attention_mask.dtype == torch.bool:
                inverted_mask = (~attention_mask).float()
            else:
                inverted_mask = 1.0 - attention_mask

            inverted_mask = inverted_mask[:, None, None, :]
            inverted_mask = inverted_mask.masked_fill(inverted_mask.to(torch.bool), torch.finfo(inputs_embeds.dtype).min)
            expanded_mask = expanded_mask + inverted_mask

        return expanded_mask
