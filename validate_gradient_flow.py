"""
Gradient Flow Validation Script

This script validates that all the fixes for gradient flow issues are working correctly.
It tests each component individually and reports which parameters receive gradients.
"""

import os
import sys
import torch
import torch.nn as nn
import logging
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from transformers import AutoTokenizer

def setup_logging():
    """Setup logging for validation."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def create_test_config():
    """Create a small test configuration."""
    return FastLLaMAConfig(
        # Small model for testing
        hidden_size=256,
        intermediate_size=256*4,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        max_position_embeddings=1024,

        # Enable ALL advanced features
        enable_context_compression=True,
        enable_early_exit=True,
        kv_cache_quantization=True,
        parameter_sharing=True,
        use_gradient_checkpointing=True,

        # Configure attention layers
        local_layers=[0, 1],
        sparse_layers=[2],
        hierarchical_layers=[3],
        full_attention_layers=[],

        # Early exit configuration
        early_exit_layers=[1, 3],
        confidence_threshold=0.7,

        # Compression configuration
        compression_ratio=4,
        compression_encoder_layers=2,
    )

def analyze_parameter_gradients(model, logger):
    """Analyze which parameters receive gradients."""
    logger.info("🔍 Analyzing parameter gradients...")
    
    categories = {
        'attention_weights': [],
        'k_quantizer': [],
        'v_quantizer': [],
        'group_adapter': [],
        'early_exit_head': [],
        'confidence_head': [],
        'context_compressor': [],
        'other': []
    }
    
    for name, param in model.named_parameters():
        if not param.requires_grad:
            logger.warning(f"❌ Parameter does not require grad: {name}")
            continue
            
        # Categorize parameters
        if 'attention_weights' in name:
            categories['attention_weights'].append(name)
        elif 'k_quantizer' in name:
            categories['k_quantizer'].append(name)
        elif 'v_quantizer' in name:
            categories['v_quantizer'].append(name)
        elif 'group_adapter' in name:
            categories['group_adapter'].append(name)
        elif 'early_exit_head' in name:
            categories['early_exit_head'].append(name)
        elif 'confidence_head' in name:
            categories['confidence_head'].append(name)
        elif 'context_compressor' in name:
            categories['context_compressor'].append(name)
        else:
            categories['other'].append(name)
    
    # Report findings
    for category, params in categories.items():
        if params:
            logger.info(f"✅ {category}: {len(params)} parameters")
            for param in params[:3]:  # Show first 3
                logger.info(f"    - {param}")
            if len(params) > 3:
                logger.info(f"    ... and {len(params) - 3} more")
        else:
            if category != 'other':
                logger.warning(f"⚠️ {category}: No parameters found")
    
    return categories

def test_gradient_flow(model, device, logger):
    """Test gradient flow through the model."""
    logger.info("🧪 Testing gradient flow...")
    
    # Create test input
    batch_size = 2
    seq_length = 512
    vocab_size = model.config.vocab_size
    
    input_ids = torch.randint(0, vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    # Forward pass
    outputs = model(input_ids=input_ids, labels=labels)
    loss = outputs["loss"]
    
    logger.info(f"✅ Forward pass successful. Loss: {loss.item():.4f}")
    
    # Backward pass
    loss.backward()
    
    # Check gradients
    grad_stats = {
        'total_params': 0,
        'params_with_grad': 0,
        'params_without_grad': [],
        'grad_norms': []
    }
    
    for name, param in model.named_parameters():
        grad_stats['total_params'] += 1
        
        if param.grad is not None:
            grad_stats['params_with_grad'] += 1
            grad_norm = param.grad.norm().item()
            grad_stats['grad_norms'].append(grad_norm)
        else:
            grad_stats['params_without_grad'].append(name)
    
    # Report gradient statistics
    logger.info(f"✅ Parameters with gradients: {grad_stats['params_with_grad']}/{grad_stats['total_params']}")
    
    if grad_stats['params_without_grad']:
        logger.error(f"❌ Parameters WITHOUT gradients:")
        for param_name in grad_stats['params_without_grad']:
            logger.error(f"    - {param_name}")
    
    if grad_stats['grad_norms']:
        avg_grad_norm = sum(grad_stats['grad_norms']) / len(grad_stats['grad_norms'])
        max_grad_norm = max(grad_stats['grad_norms'])
        min_grad_norm = min(grad_stats['grad_norms'])
        
        logger.info(f"📊 Gradient norms - Avg: {avg_grad_norm:.6f}, Max: {max_grad_norm:.6f}, Min: {min_grad_norm:.6f}")
    
    return len(grad_stats['params_without_grad']) == 0

def test_attention_mechanisms(model, device, logger):
    """Test that attention mechanisms are activated."""
    logger.info("🔍 Testing attention mechanism activation...")
    
    # Test with different sequence lengths
    test_lengths = [256, 512, 1024]
    vocab_size = model.config.vocab_size
    
    for seq_len in test_lengths:
        logger.info(f"Testing sequence length: {seq_len}")
        
        input_ids = torch.randint(0, vocab_size, (1, seq_len)).to(device)
        
        model.eval()
        with torch.no_grad():
            outputs = model(input_ids=input_ids)
        
        # Check if early exit outputs are generated
        if outputs.get("early_exit_outputs"):
            logger.info(f"  ✅ Early exit activated: {len(outputs['early_exit_outputs'])} exits")
        else:
            logger.info(f"  ⚠️ No early exit outputs")
        
        # Check compression quality
        if outputs.get("compression_quality") is not None:
            logger.info(f"  ✅ Context compression activated: quality={outputs['compression_quality']}")
        else:
            logger.info(f"  ⚠️ No context compression")

def test_early_exit_training(model, device, logger):
    """Test that early exit heads are properly trained."""
    logger.info("🧪 Testing early exit training...")
    
    batch_size = 2
    seq_length = 512
    vocab_size = model.config.vocab_size
    
    input_ids = torch.randint(0, vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()
    
    model.train()
    model.zero_grad()
    
    # Forward pass
    outputs = model(input_ids=input_ids, labels=labels)
    
    # Check early exit outputs
    if outputs.get("early_exit_outputs"):
        logger.info(f"✅ Early exit outputs generated: {len(outputs['early_exit_outputs'])}")
        
        # Test early exit loss computation
        main_loss = outputs["loss"]
        total_loss = main_loss
        
        for i, early_output in enumerate(outputs["early_exit_outputs"]):
            early_logits = early_output["logits"]
            confidence = early_output["confidence"]
            
            logger.info(f"  Exit {i}: logits shape={early_logits.shape}, confidence={confidence.mean().item():.3f}")
            
            # Compute early exit loss
            shift_logits = early_logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()
            
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
            early_loss = loss_fct(shift_logits.view(-1, vocab_size), shift_labels.view(-1))
            total_loss += 0.1 * early_loss
        
        # Backward pass
        total_loss.backward()
        
        # Check gradients on early exit heads
        early_exit_grads = 0
        for name, param in model.named_parameters():
            if 'early_exit_head' in name or 'confidence_head' in name:
                if param.grad is not None:
                    early_exit_grads += 1
                else:
                    logger.error(f"❌ No gradient for early exit parameter: {name}")
        
        logger.info(f"✅ Early exit parameters with gradients: {early_exit_grads}")
        
    else:
        logger.warning("⚠️ No early exit outputs generated")

def main():
    """Main validation function."""
    logger = setup_logging()
    logger.info("🔧 FastLLaMA Gradient Flow Validation")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Create test model
    config = create_test_config()
    model = FastLLaMAModel(config)
    model.to(device)
    
    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"✅ Test model created: {num_params/1e6:.1f}M parameters")
    
    # Run validation tests
    logger.info("\n" + "="*50)
    logger.info("VALIDATION TESTS")
    logger.info("="*50)
    
    # Test 1: Parameter analysis
    logger.info("\n1. Parameter Analysis")
    categories = analyze_parameter_gradients(model, logger)
    
    # Test 2: Gradient flow
    logger.info("\n2. Gradient Flow Test")
    gradient_flow_ok = test_gradient_flow(model, device, logger)
    
    # Test 3: Attention mechanisms
    logger.info("\n3. Attention Mechanism Test")
    test_attention_mechanisms(model, device, logger)
    
    # Test 4: Early exit training
    logger.info("\n4. Early Exit Training Test")
    test_early_exit_training(model, device, logger)
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("VALIDATION SUMMARY")
    logger.info("="*50)
    
    if gradient_flow_ok:
        logger.info("✅ GRADIENT FLOW: PASSED")
    else:
        logger.error("❌ GRADIENT FLOW: FAILED")
    
    # Check critical parameters
    critical_params = ['attention_weights', 'k_quantizer', 'v_quantizer', 'early_exit_head']
    for param_type in critical_params:
        if categories[param_type]:
            logger.info(f"✅ {param_type.upper()}: FOUND")
        else:
            logger.warning(f"⚠️ {param_type.upper()}: NOT FOUND")
    
    if gradient_flow_ok and all(categories[p] for p in critical_params if p in categories):
        logger.info("\n🎉 ALL VALIDATION TESTS PASSED!")
        logger.info("✅ Ready for training with advanced features")
    else:
        logger.error("\n❌ VALIDATION FAILED!")
        logger.error("🔧 Additional fixes needed")

if __name__ == "__main__":
    main()
